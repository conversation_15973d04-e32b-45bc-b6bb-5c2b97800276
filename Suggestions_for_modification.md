作为你的收益管理经理，我为你准备了这两个模块的代码实现方案。这些代码可以直接整合进你现有的 `analyze_fangte.py` 脚本中，风格和结构都与你之前的代码保持一致。

---

### **如何整合代码**

1.  **复制函数定义**：将下面提供的两个新函数 `analyze_niche_segments` 和 `analyze_yoy_strategy_evolution` 的完整代码，粘贴到你的脚本中，可以放在 `main` 逻辑开始之前，与其他函数定义放在一起。
2.  **在主流程中调用函数**：
    *   在 `## 核心票种价格策略分析` 模块之后，添加对 `analyze_niche_segments(promotions)` 的调用。
    *   在 `## 节假日价格指数分析` 模块之后，添加对 `analyze_yoy_strategy_evolution(promotions)` 的调用（**必须放在节假日分析之后**，因为它需要用到节假日分析中生成的`适用节假日`列）。

下面是具体的代码和详细解释。

---

### **模块一：特定客群深度分析 (代码实现)**

这个模块的目的是识别并剖析竞品针对“非大散”的细分客群（如学生、女性）的精准打击策略。

```python
# ==============================================================================
# 新增模块：特定客群深度分析
# ==============================================================================
def analyze_niche_segments(df):
    """
    分析特定细分客群的促销策略，如大学生、中高考生、女士票等。
    """
    print_to_md("## 特定客群深度分析")
    print_to_md("> **收益管理洞察**: 竞品通过对特定客群（如学生、女性）的精准低价策略，能够有效渗透增量市场，撬动其背后的家庭或社交消费。我们需要识别这些'战术核武器'，评估其威胁等级。")
    print_to_md()

    # 定义需要分析的特定客群关键词
    niche_segments = {
        '中高考生票': '中高考',
        '大学生票': '大学',
        '女士票': '女士',
        '特殊人群票': '特殊人群'  # 如军人、残疾人等
    }

    for segment_name, keyword in niche_segments.items():
        segment_data = df[df['促销票种'].str.contains(keyword, na=False)].copy()

        if segment_data.empty:
            continue  # 如果没有该类票种的数据，则跳过

        print_to_md(f"### {segment_name} 策略分析")

        # 计算核心指标
        promo_count = len(segment_data)
        avg_discount = segment_data['折扣率'].mean()
        min_price = segment_data['促销价格'].min()
      
        # 分析主要推出时间窗口
        # .dt.month获取月份，.mode()取众数，[0]取第一个结果
        if not segment_data['销售日期（起）'].dt.month.mode().empty:
            main_launch_month = segment_data['销售日期（起）'].dt.month.mode()[0]
            launch_window_desc = f"{main_launch_month}月"
        else:
            launch_window_desc = "N/A"

        # 策略解读
        if '中高考' in keyword:
            insight = "精准收割考后暑期市场，是撬动家庭出游的关键引流点。"
        elif '大学' in keyword:
            insight = "针对高校开学季、周末及寒暑假，培养年轻客群消费习惯。"
        elif '女士' in keyword:
            insight = "围绕'三八'妇女节等节点，激发'她经济'消费潜力。"
        else:
            insight = "履行社会责任的同时，激活特定圈层市场。"

        print_to_md(f"- **促销次数**: {promo_count}次")
        print_to_md(f"- **平均折扣**: {avg_discount:.1%} (折扣力度极大)")
        print_to_md(f"- **最低价格**: {min_price}元")
        print_to_md(f"- **主要推出月份**: {launch_window_desc}")
        print_to_md(f"- **策略洞察**: {insight}")
        print_to_md()

# ==============================================================================
# 调用位置建议：
# 在你的主代码逻辑中，紧跟在 “## 核心票种价格策略分析” 模块后面
# analyze_niche_segments(promotions)
# ==============================================================================
```

### **模块二：年度策略演变分析 (代码实现)**

这个模块的目的是通过纵向对比，判断竞品在同一个节假日或主题活动上的定价信心是增强了还是减弱了，从而洞察市场趋势。

```python
# ==============================================================================
# 新增模块：年度策略演变分析 (YoY)
# ==============================================================================
def analyze_yoy_strategy_evolution(df):
    """
    对比分析同一节假日或主题活动在不同年份的定价策略变化。
    """
    print_to_md("## 年度策略演变分析 (YoY)")
    print_to_md("> **收益管理洞察**: 通过纵向对比，我们可以判断竞品的市场信心和策略方向。价格逐年上涨，代表其品牌溢价能力和市场需求旺盛；反之则可能面临竞争压力或策略调整。")
    print_to_md()

    # --- 1. 分析节假日的年度演变 ---
    print_to_md("### 核心节假日策略演变 (YoY)")
  
    # 确保 '适用节假日' 列存在且已被处理
    if '适用节假日' not in df.columns:
        print_to_md("- *无法进行节假日YoY分析，缺少'适用节假日'列。请确保此函数在节假日分析后运行。*")
        return

    holiday_promos = df.dropna(subset=['适用节假日']).explode('适用节假日')
    if not holiday_promos.empty:
        holiday_promos[['节假日基础名', '年份']] = holiday_promos['适用节假日'].str.split('-', n=1, expand=True)
      
        # 找到出现超过1次的节假日
        recurring_holidays = holiday_promos['节假日基础名'].value_counts()
        yoy_holidays = recurring_holidays[recurring_holidays > 1].index

        if len(yoy_holidays) > 0:
            for holiday in yoy_holidays:
                print_to_md(f"#### {holiday} YoY 对比")
                holiday_data = holiday_promos[holiday_promos['节假日基础名'] == holiday]
              
                # 按年份聚合，计算核心指标
                yoy_stats = holiday_data.groupby('年份').agg(
                    平均促销价=('促销价格', 'mean'),
                    平均折扣率=('折扣率', 'mean'),
                    促销次数=('促销票种', 'count')
                ).round(2)
              
                # 计算同比变化
                yoy_stats['价格同比变化'] = yoy_stats['平均促销价'].pct_change().mul(100).round(1).fillna(0)
              
                print_to_md("| 年份 | 平均价格 | 价格同比 | 平均折扣率 | 促销次数 |")
                print_to_md("|:---|---:|---:|---:|---:|")
                for year, row in yoy_stats.iterrows():
                    change_str = f"{row['价格同比变化']:+.1f}%" if row['价格同比变化'] != 0 else "---"
                    print_to_md(f"| {year} | {row['平均促销价']:.0f}元 | **{change_str}** | {row['平均折扣率']:.1%} | {row['促销次数']:.0f}次 |")
                print_to_md()
        else:
            print_to_md("- *未发现可供同比分析的核心节假日。*")
        print_to_md()


    # --- 2. 分析主题活动的年度演变 ---
    print_to_md("### 主题活动策略演变 (YoY)")
  
    # 使用你已有的 '带年份主题活动' 列
    theme_promos = df.dropna(subset=['主题活动']).copy()
    if not theme_promos.empty:
        theme_promos['年份'] = theme_promos['使用日期（起）'].dt.year
      
        # 找到出现超过1次的主题活动
        recurring_themes = theme_promos['主题活动'].value_counts()
        yoy_themes = recurring_themes[recurring_themes > 1].index

        if len(yoy_themes) > 0:
            for theme in yoy_themes:
                print_to_md(f"#### {theme} YoY 对比")
                theme_data = theme_promos[theme_promos['主题活动'] == theme]
              
                yoy_stats = theme_data.groupby('年份').agg(
                    平均促销价=('促销价格', 'mean'),
                    平均折扣率=('折扣率', 'mean'),
                    平均预售天数=('预售天数', 'mean'),
                    促销次数=('促销票种', 'count')
                ).round(2)

                yoy_stats['价格同比变化'] = yoy_stats['平均促销价'].pct_change().mul(100).round(1).fillna(0)

                print_to_md("| 年份 | 平均价格 | 价格同比 | 平均折扣率 | 平均预售 | 促销次数 |")
                print_to_md("|:---|---:|---:|---:|---:|---:|")
                for year, row in yoy_stats.iterrows():
                    change_str = f"{row['价格同比变化']:+.1f}%" if row['价格同比变化'] != 0 else "---"
                    print_to_md(f"| {year} | {row['平均促销价']:.0f}元 | **{change_str}** | {row['平均折扣率']:.1%} | {row['平均预售天数']:.1f}天 | {row['促销次数']:.0f}次 |")
                print_to_md()
        else:
            print_to_md("- *未发现可供同比分析的主题活动。*")
  
    print_to_md()

# ==============================================================================
# 调用位置建议：
# 在你的主代码逻辑中，紧跟在 “## 节假日价格指数分析” 模块后面
# analyze_yoy_strategy_evolution(promotions)
# ==============================================================================
```

### **总结**

将这两个模块加入你的分析脚本后，你的报告将新增两个非常重要的视角：

1.  **客群打击精度**：你能清晰地看到竞品是如何用“小切口”产品撬动特定市场的，这对于我们设计防御性或竞争性产品至关重要。
2.  **策略动态趋势**：你能看到竞品定价策略的“时间轴”，是越来越激进（提价）还是越来越保守（降价），这为我们判断整个市场的竞争态势提供了有力依据。

这会让我们的决策更有前瞻性和针对性。去把代码加上，然后把更新后的报告发给我。