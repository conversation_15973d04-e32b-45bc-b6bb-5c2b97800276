import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

# 连接数据库
conn = sqlite3.connect('Competitor_Zigong Fangte_Promotion Details.db')

# 创建输出文件
output_file = open('自贡方特促销分析报告.md', 'w', encoding='utf-8')

def print_to_md(text="", end='\n'):
    """将内容只输出到MD文件"""
    output_file.write(text + end)

# 读取数据
promotions = pd.read_sql_query("SELECT * FROM promotions", conn)
monthly_discounts = pd.read_sql_query("SELECT * FROM monthly_discounts", conn)
monthly_sales_days = pd.read_sql_query("SELECT * FROM monthly_sales_days", conn)

# 【新增】加载节假日数据
holidays_df = pd.read_csv('holidays.csv')

# 开始写入MD文件
print_to_md("# 自贡方特促销分析报告")
print_to_md(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print_to_md()

print_to_md("## 数据库基础统计")
print_to_md(f"- 促销活动总数: {len(promotions)}")
print_to_md(f"- 月度折扣数据条数: {len(monthly_discounts)}")
print_to_md(f"- 月度销售天数数据条数: {len(monthly_sales_days)}")
print_to_md()

print_to_md("## 促销票种分析")
promotions['折扣率'] = promotions['促销价格'] / promotions['门市价']
print_to_md("### 所有票种类型")
ticket_types = promotions['促销票种'].unique()
for i, ticket in enumerate(ticket_types, 1):
    print_to_md(f"{i}. {ticket}")
print_to_md()

# 转换日期格式并计算销售天数
promotions['销售日期（起）'] = pd.to_datetime(promotions['销售日期（起）'])
promotions['销售日期（止）'] = pd.to_datetime(promotions['销售日期（止）'])
promotions['销售天数'] = (promotions['销售日期（止）'] - promotions['销售日期（起）']).dt.days + 1

# 【新增代码】处理使用日期并计算预售天数
# ==============================================================================
promotions['使用日期（起）'] = pd.to_datetime(promotions['使用日期（起）'])
promotions['使用日期（止）'] = pd.to_datetime(promotions['使用日期（止）'])

# 计算预售天数，即销售第一天距离使用第一天有多久
promotions['预售天数'] = (promotions['使用日期（起）'] - promotions['销售日期（起）']).dt.days

# 数据清洗：预售天数不应为负，如果是负数或空值，则当做0天（即买即用）
promotions['预售天数'] = promotions['预售天数'].apply(lambda x: x if pd.notna(x) and x >= 0 else 0)
# ==============================================================================

def get_strategy_tag(sales_days):
    """
    根据销售天数生成策略标签
    闪促型: 1-3天 - 制造稀缺感，瞬间引爆流量
    节点型: 4-10天 - 覆盖完整节假日或黄金周
    波段型: 11-30天 - 配合完整主题活动营销周期
    长线型: >30天 - 预售周期长、决策周期也长的产品
    """
    if 1 <= sales_days <= 3:
        return "闪促型"
    elif 4 <= sales_days <= 10:
        return "节点型"
    elif 11 <= sales_days <= 30:
        return "波段型"
    else:
        return "长线型"

print_to_md("## 核心票种价格策略分析")
# 核心票种定义（全局使用）
core_tickets = ['成人票', '亲子票', '双人票', '家庭2大1小票', '单人夜场票', '儿童票']
promotion_analysis = []

for ticket in core_tickets:
    # 【关键修改#1】使用 .str.contains() 进行模糊匹配，确保数据完整性
    ticket_data = promotions[promotions['促销票种'].str.contains(ticket, na=False)].copy()
  
    if len(ticket_data) > 0:
        # 【优化修改#2】先为每一次促销打上标签，再取众数
        ticket_data.loc[:, '策略标签'] = ticket_data['销售天数'].apply(get_strategy_tag)
        strategy_tag = ticket_data['策略标签'].mode()[0] if not ticket_data['策略标签'].mode().empty else "混合型"

        # --- 其他指标计算 ---
        # 使用销售天数作为权重，计算加权平均折扣率
        avg_discount = np.average(ticket_data['折扣率'], weights=ticket_data['销售天数'])
        min_discount = ticket_data['折扣率'].min()
        max_price = ticket_data['门市价'].max()
        min_price = ticket_data['促销价格'].min()
        count = len(ticket_data)
        avg_sales_days = ticket_data['销售天数'].mean()
        price_anchor = ticket_data['促销价格'].mode()[0] if not ticket_data['促销价格'].mode().empty else ticket_data['促销价格'].median()
        avg_promotion_price = np.average(ticket_data['促销价格'], weights=ticket_data['销售天数'])
      
        print_to_md(f"### {ticket}")
        print_to_md(f"- 促销次数: {count}")
        print_to_md(f"- 平均折扣率: {avg_discount:.2%}")
        print_to_md(f"- 最低折扣率: {min_discount:.2%}")
        print_to_md(f"- 最高门市价: {max_price}元")
        print_to_md(f"- 最低促销价: {min_price}元")
        print_to_md(f"- 平均促销价(加权): {avg_promotion_price:.1f}元")
        print_to_md(f"- 价格锚点(众数): {price_anchor:.1f}元")
        print_to_md(f"- 平均销售天数: {avg_sales_days:.1f}天")
        print_to_md(f"- 策略标签(主要): {strategy_tag}")
        print_to_md()
      
        # --- 汇总数据 ---
        promotion_analysis.append({
            '票种': ticket,
            '促销次数': count,
            '平均折扣率': avg_discount,
            '最低折扣率': min_discount,
            '最高门市价': max_price,
            '最低促销价': min_price,
            '平均促销价': avg_promotion_price,
            '价格锚点': price_anchor,
            '平均销售天数': avg_sales_days,
            '策略标签': strategy_tag  # 使用计算出的众数标签
        })

# ==============================================================================
# 新增模块：特定客群深度分析
# ==============================================================================
def analyze_niche_segments(df):
    """
    分析特定细分客群的促销策略，如大学生、中高考生、女士票等。
    """
    print_to_md("## 特定客群深度分析")
    print_to_md("> **收益管理洞察**: 竞品通过对特定客群（如学生、女性）的精准低价策略，能够有效渗透增量市场，撬动其背后的家庭或社交消费。我们需要识别这些'战术核武器'，评估其威胁等级。")
    print_to_md()

    # 定义需要分析的特定客群关键词
    niche_segments = {
        '中高考生票': '中高考',
        '大学生票': '大学',
        '女士票': '女士',
        '特殊人群票': '特殊人群'  # 如军人、残疾人等
    }

    for segment_name, keyword in niche_segments.items():
        segment_data = df[df['促销票种'].str.contains(keyword, na=False)].copy()

        if segment_data.empty:
            continue  # 如果没有该类票种的数据，则跳过

        print_to_md(f"### {segment_name} 策略分析")

        # 计算核心指标
        promo_count = len(segment_data)
        avg_discount = segment_data['折扣率'].mean()
        min_price = segment_data['促销价格'].min()
      
        # 分析主要推出时间窗口
        # .dt.month获取月份，.mode()取众数，[0]取第一个结果
        if not segment_data['销售日期（起）'].dt.month.mode().empty:
            main_launch_month = segment_data['销售日期（起）'].dt.month.mode()[0]
            launch_window_desc = f"{main_launch_month}月"
        else:
            launch_window_desc = "N/A"

        # 策略解读
        if '中高考' in keyword:
            insight = "精准收割考后暑期市场，是撬动家庭出游的关键引流点。"
        elif '大学' in keyword:
            insight = "针对高校开学季、周末及寒暑假，培养年轻客群消费习惯。"
        elif '女士' in keyword:
            insight = "围绕'三八'妇女节等节点，激发'她经济'消费潜力。"
        else:
            insight = "履行社会责任的同时，激活特定圈层市场。"

        print_to_md(f"- **促销次数**: {promo_count}次")
        print_to_md(f"- **平均折扣**: {avg_discount:.1%} (折扣力度极大)")
        print_to_md(f"- **最低价格**: {min_price}元")
        print_to_md(f"- **主要推出月份**: {launch_window_desc}")
        print_to_md(f"- **策略洞察**: {insight}")
        print_to_md()

print_to_md("## 票种策略重要性评级计算")
# 为票种计算策略重要性评级
def calculate_ticket_strategy_score(row):
    """
    计算票种策略重要性评级分数
    权重分配：
    - 折扣力度 (50%): 平均折扣率越低，策略重要性越高，体现其作为价格武器的威力
    - 促销频率 (30%): 促销次数反映策略使用频度，体现其市场应用的广度
    - 价格敏感度 (20%): 基于最低折扣率判断价格弹性，体现其撬动极限需求的能力
    """
    
    # 1. 折扣力度分数 (平均折扣率越低即折扣越大，分数越高)
    avg_discount_score = (1 - row['平均折扣率']) * 100
    
    # 2. 促销频率分数 (标准化处理，基于所有票种的最大促销次数)
    max_promotion_count = max([item['促销次数'] for item in promotion_analysis])
    promotion_frequency_score = (row['促销次数'] / max_promotion_count) * 100
    
    # 3. 价格敏感度分数 (最低折扣率越低，说明价格弹性越大，策略重要性越高)
    price_sensitivity_score = (1 - row['最低折扣率']) * 100
    
    # 加权计算最终分数
    final_score = (
        avg_discount_score * 0.50 +
        promotion_frequency_score * 0.30 +
        price_sensitivity_score * 0.20
    )
    
    return {
        '平均折扣力度分数': round(avg_discount_score, 1),
        '促销频率分数': round(promotion_frequency_score, 1),
        '价格敏感度分数': round(price_sensitivity_score, 1),
        '综合评分': round(final_score, 1)
    }

# 计算每个票种的评级
ticket_scores = []
print_to_md("### 票种策略重要性评级计算结果")
print_to_md()
print_to_md("#### 评级权重说明")
print_to_md("- 平均折扣力度：50%（体现其作为价格武器的威力）")
print_to_md("- 促销频率：30%（体现其市场应用的广度）")
print_to_md("- 价格敏感度：20%（体现其撬动极限需求的能力）")
print_to_md()

for ticket_data in promotion_analysis:
    scores = calculate_ticket_strategy_score(ticket_data)
    
    # 根据综合评分确定等级
    final_score = scores['综合评分']
    if final_score >= 85:
        grade = "A+"
    elif final_score >= 75:
        grade = "A"
    elif final_score >= 65:
        grade = "B+"
    elif final_score >= 55:
        grade = "B"
    elif final_score >= 45:
        grade = "C+"
    else:
        grade = "C"
    
    # 计算预估收益指数
    estimated_revenue_index = ticket_data['平均促销价'] * ticket_data['促销次数']
    
    ticket_scores.append({
        '票种': ticket_data['票种'],
        '促销次数': ticket_data['促销次数'],
        '平均折扣率': f"{ticket_data['平均折扣率']:.1%}",
        '平均销售天数': ticket_data['平均销售天数'],
        '平均折扣力度分数': scores['平均折扣力度分数'],
        '促销频率分数': scores['促销频率分数'],
        '价格敏感度分数': scores['价格敏感度分数'],
        '综合评分': scores['综合评分'],
        '策略重要性等级': grade,
        '预估收益指数': estimated_revenue_index,
        '平均促销价': ticket_data['平均促销价'],
        '策略标签': ticket_data['策略标签']
    })

# 按综合评分排序并展示
ticket_df = pd.DataFrame(ticket_scores).sort_values('综合评分', ascending=False)

print_to_md("#### 票种策略重要性评级结果表")
print_to_md()
print_to_md("| 票种 | 次数 | 折扣率 | 天数 | 主要标签 | 折扣分 | 频率分 | 敏感分 | 综合分 | 等级 | 收益指数 |")
print_to_md("|------|------|--------|------|------|--------|--------|--------|--------|------|----------|")

for _, row in ticket_df.iterrows():
    print_to_md(f"| {row['票种']} | {row['促销次数']:.0f} | {row['平均折扣率']} | {row['平均销售天数']:.1f} | {row['策略标签']} | "
              f"{row['平均折扣力度分数']:.1f} | {row['促销频率分数']:.1f} | "
              f"{row['价格敏感度分数']:.1f} | {row['综合评分']:.1f} | {row['策略重要性等级']} | {row['预估收益指数']:.0f} |")

print_to_md()

# 调用特定客群深度分析
analyze_niche_segments(promotions)

# 增加价格波动性分析模块
def create_price_boxplot(df, core_tickets, filename="自贡方特_价格波动箱形图.png"):
    """
    为核心票种生成价格波动箱形图
    """
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
  
    plt.figure(figsize=(16, 9))
  
    # 筛选核心票种数据
    plot_data = df[df['促销票种'].str.contains('|'.join(core_tickets), na=False)]
  
    if len(plot_data) == 0:
        print("没有找到核心票种数据，跳过箱形图生成")
        return
    
    sns.boxplot(data=plot_data, x='促销价格', y='促销票种', palette='coolwarm', orient='h')
  
    plt.title('核心票种促销价格波动性分析', fontsize=20)
    plt.xlabel('促销价格 (元)', fontsize=14)
    plt.ylabel('票种', fontsize=14)
    plt.grid(axis='x', linestyle='--', alpha=0.6)
    plt.tight_layout()
    plt.savefig(filename, dpi=300)
    plt.close()
    print(f"价格波动箱形图已保存为: {filename}")
    print_to_md(f"![价格波动箱形图](./{filename})")

print_to_md("## 价格波动性分析")
print_to_md("> 箱形图直观展示了各类票种促销价格的分布范围。**'盒子'越窄，说明价格策略越稳定；'胡须'越长或异常点越多，说明其价格弹性越大，调价越灵活。**")
print_to_md()
create_price_boxplot(promotions, core_tickets)
print_to_md()

print_to_md("## 月度折扣率分析")
# 分析月度折扣趋势
monthly_avg = monthly_discounts.groupby(['year', 'month'])['discount_rate'].mean().reset_index()
monthly_avg['年月'] = monthly_avg['year'].astype(str) + '-' + monthly_avg['month'].astype(str).str.zfill(2)
print_to_md("### 月度平均折扣率")
print_to_md()
for _, row in monthly_avg.iterrows():
    print_to_md(f"- {row['年月']}: {row['discount_rate']:.2%}")
print_to_md()

print_to_md("## 销售天数分析")
# 分析各票种销售天数
ticket_sales_summary = monthly_sales_days.groupby('ticket_type')['sales_days'].agg(['sum', 'mean', 'count']).round(1)
print_to_md("### 各票种销售天数统计")
print_to_md()
for ticket_type, row in ticket_sales_summary.head(10).iterrows():
    print_to_md(f"- {ticket_type}: 总天数={row['sum']}, 平均天数={row['mean']}, 次数={row['count']}")

print_to_md()
print_to_md("## 时间节点分析")
# 使用monthly_discounts和monthly_sales_days表进行正确的月度统计
print_to_md("### 基于monthly_discounts表的月度促销统计")
print_to_md()

# 统计每月促销次数（基于monthly_discounts表）
monthly_promotion_count = monthly_discounts.groupby('month').size().reset_index()
monthly_promotion_count.columns = ['月份', '促销次数']

# 统计每月平均折扣率
monthly_avg_discount = monthly_discounts.groupby('month')['discount_rate'].mean().reset_index()
monthly_avg_discount.columns = ['月份', '平均折扣率']

# 统计每月平均销售天数
monthly_avg_sales_days = monthly_sales_days.groupby('month')['sales_days'].mean().reset_index()
monthly_avg_sales_days.columns = ['月份', '平均销售天数']

# 合并统计结果
monthly_stats = monthly_promotion_count.merge(monthly_avg_discount, on='月份', how='left')
monthly_stats = monthly_stats.merge(monthly_avg_sales_days, on='月份', how='left')
monthly_stats = monthly_stats.round(2)

print_to_md("#### 各月份促销统计（基于汇总表）")
print_to_md()
print_to_md("| 月份 | 促销次数 | 平均折扣率 | 平均销售天数 | 样本状态 |")
print_to_md("|------|----------|------------|--------------|----------|")
for _, row in monthly_stats.iterrows():
    count = int(row['促销次数'])
    discount = row['平均折扣率']
    days = row['平均销售天数']
    sample_status = "充足样本" if count >= 3 else "样本过小"
    print_to_md(f"| {int(row['月份'])}月 | {count}次 | {discount:.0%} | {days:.1f}天 | {sample_status} |")

print_to_md()
print_to_md("## 主题活动分析")

# 自定义函数计算加权平均折扣率
def weighted_avg_discount(group):
    return np.average(group['折扣率'], weights=group['销售天数'])

# 【修改：为主题活动统计也添加年份前缀】
promotions_with_year = promotions.copy()
promotions_with_year['年份'] = promotions_with_year['使用日期（起）'].dt.year
promotions_with_year['带年份主题活动'] = promotions_with_year['年份'].astype(str) + '-' + promotions_with_year['主题活动'].fillna('无主题活动')

theme_analysis = promotions_with_year.groupby('带年份主题活动').agg({
    '促销票种': 'count',
    '销售天数': 'mean'
}).round(2)

# 单独计算加权平均折扣率
weighted_discounts = promotions_with_year.groupby('带年份主题活动').apply(weighted_avg_discount, include_groups=False).round(4)

# 合并结果
theme_analysis['平均折扣率'] = weighted_discounts
theme_analysis = theme_analysis[['促销票种', '平均折扣率', '销售天数']].sort_values('促销票种', ascending=False)
theme_analysis.columns = ['促销票种数', '平均折扣率', '平均销售天数']
print_to_md("### 主题活动统计(按票种数排序)")
print_to_md()
for activity, row in theme_analysis.head(10).iterrows():
    print_to_md(f"- {activity}: 票种数={row['促销票种数']}, 平均折扣率={row['平均折扣率']:.2%}, 平均销售天数={row['平均销售天数']:.1f}")

print_to_md()

# 【新增函数】预售期分析模块
# ==============================================================================
def analyze_lead_time(df, filename="自贡方特_预售期分析图.png"):
    """
    生成预售期（Lead Time）分析报告和图表
    """
    print_to_md("## 预售期 (Lead Time) 策略分析")
    print_to_md("> 预售期是衡量竞品市场预判、提前锁定收益能力的关键指标。**预售期越长，通常代表该产品或活动的重要性越高，竞品希望通过拉长销售周期来最大化收益。**")
    print_to_md()

    # 1. 整体预售策略概览
    avg_lead_time = df['预售天数'].mean()
    median_lead_time = df['预售天数'].median()
    max_lead_time = df['预售天数'].max()
    print_to_md("### 整体预售策略概览")
    print_to_md(f"- **平均预售天数**: {avg_lead_time:.1f} 天")
    print_to_md(f"- **预售天数中位数**: {median_lead_time:.1f} 天 (此数据更能反映普遍情况，排除极端值干扰)")
    print_to_md(f"- **最长预售天数**: {max_lead_time:.0f} 天")
    print_to_md()

    # 2. 按主题活动分析预售期
    print_to_md("### 核心主题活动预售期对比")
    # 过滤掉没有主题活动的促销，并按预售期长短排序
    # 【关键修改：为活动名称添加年份前缀】
    df_with_year = df[df['主题活动'].notna()].copy()
    df_with_year['年份'] = df_with_year['使用日期（起）'].dt.year
    df_with_year['带年份活动名'] = df_with_year['年份'].astype(str) + '-' + df_with_year['主题活动']
    
    theme_lead_time = df_with_year.groupby('带年份活动名')['预售天数'].mean().sort_values(ascending=False).round(1)
  
    print_to_md("| 主题活动 | 平均预售天数 | 策略解读 |")
    print_to_md("|:---|:---:|:---|")
    for theme, days in theme_lead_time.head(10).items():
        if days >= 30:
            interpretation = "战略级活动，超长周期锁定核心客群"
        elif 15 <= days < 30:
            interpretation = "重点规划活动，提前抢占市场声量"
        elif 7 <= days < 15:
            interpretation = "常规节点活动，节奏稳健"
        else:
            interpretation = "战术性/即时性活动"
        print_to_md(f"| {theme} | **{days}天** | {interpretation} |")
    print_to_md()

    # 3. 生成并保存可视化图表
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
  
    plt.figure(figsize=(16, 9))
    top_themes = theme_lead_time.head(10)
  
    # 创建条形图
    ax = sns.barplot(x=top_themes.values, y=top_themes.index, palette='crest_r')
  
    # 设置图表标题和标签
    plt.title('Top 10 主题活动平均预售天数对比', fontsize=20, pad=20)
    plt.xlabel('平均预售天数 (天)', fontsize=14)
    plt.ylabel('主题活动', fontsize=14)
  
    # 在条形图上添加数据标签
    for p in ax.patches:
        width = p.get_width()
        plt.text(width + 0.3, p.get_y() + p.get_height()/2.,
                 f'{width:.1f}',
                 va='center', fontsize=12)

    plt.grid(axis='x', linestyle='--', alpha=0.6)
    plt.tight_layout()
    plt.savefig(filename, dpi=300)
    plt.close()
  
    print(f"预售期分析图已保存为: {filename}")
    print_to_md(f"![核心主题活动预售期对比](./{filename})")
    print_to_md()
# ==============================================================================

# 【新增调用】
# ==============================================================================
# 调用新增的预售期分析函数
analyze_lead_time(promotions)

# 【新增】节假日价格指数分析模块
# ==============================================================================
def find_applicable_holidays(promo_row, holidays_df):
    """
    检查单条促销活动覆盖了哪些节假日
    区间A [start1, end1] 和区间B [start2, end2] 重叠的条件是:
    start1 <= end2 AND end1 >= start2
    """
    promo_start = promo_row['使用日期（起）']
    promo_end = promo_row['使用日期（止）']
  
    # 筛选出所有与该促销活动日期有重叠的节假日
    applicable = holidays_df[
        (promo_start <= holidays_df['end_date']) & 
        (promo_end >= holidays_df['start_date'])
    ]
  
    # 如果有多个，可以返回列表，或只返回第一个，或拼接成字符串
    if not applicable.empty:
        # 拼接节假日名称和年份，例如 "国庆节-2023"
        return applicable.apply(lambda r: f"{r['holiday_name']}-{r['year']}", axis=1).tolist()
    return None

def analyze_holiday_price_index():
    """
    节假日价格指数分析的核心函数
    """
    print_to_md("## 节假日价格指数分析")
    print_to_md("> **核心理念**: 通过对比各节假日期间的促销价格与全年平均水平，识别方特的**价格战略信号**。指数>1.2为核心收割期，≈1.0为流量争夺期，<1.0为市场激活期。")
    print_to_md()
    
    # 加权平均辅助函数
    def weighted_average(group, avg_col, weight_col):
        """计算加权平均的辅助函数"""
        return np.average(group[avg_col], weights=group[weight_col])
    
    # 确保日期类型正确
    holidays_df['start_date'] = pd.to_datetime(holidays_df['start_date'])
    holidays_df['end_date'] = pd.to_datetime(holidays_df['end_date'])
    
    # 将节假日匹配函数应用到促销数据上
    print("正在匹配促销活动与节假日...")
    promotions['适用节假日'] = promotions.apply(find_applicable_holidays, holidays_df=holidays_df, axis=1)
    
    # 清理数据，去掉没有适用节假日的促销，并展开多个节假日
    holiday_promos_df = promotions.dropna(subset=['适用节假日']).copy()
    holiday_promos_df = holiday_promos_df.explode('适用节假日')
    
    print_to_md(f"### 节假日促销匹配统计")
    print_to_md(f"- 总促销活动数: {len(promotions)}")
    print_to_md(f"- 节假日相关促销数: {len(holiday_promos_df)}")
    print_to_md(f"- 节假日促销占比: {len(holiday_promos_df)/len(promotions):.1%}")
    print_to_md()
    
    print_to_md("### 核心票种节假日价格指数")
    print_to_md()
    
    for ticket in core_tickets:
        print_to_md(f"#### {ticket}")
        
        # 计算基准价格（全时段平均）
        base_ticket_data = promotions[promotions['促销票种'].str.contains(ticket, na=False)]
        if len(base_ticket_data) == 0:
            print_to_md(f"- 无{ticket}数据")
            print_to_md()
            continue
            
        base_price = np.average(base_ticket_data['促销价格'], weights=base_ticket_data['销售天数'])
        print_to_md(f"- **基准价格** (全年加权平均): {base_price:.1f}元")
        
        # 计算各节假日期间的价格
        holiday_ticket_data = holiday_promos_df[holiday_promos_df['促销票种'].str.contains(ticket, na=False)]
        
        if len(holiday_ticket_data) == 0:
            print_to_md(f"- 无节假日期间的{ticket}数据")
            print_to_md()
            continue
            
        # 按节假日分组，分别计算加权平均价、次数和总销售天数
        holiday_stats = holiday_ticket_data.groupby('适用节假日').apply(
            lambda g: pd.Series({
                '平均价格': weighted_average(g, '促销价格', '销售天数'),
                '促销次数': len(g),
                '总销售天数': g['销售天数'].sum()
            }), include_groups=False
        ).round(1)
        
        holiday_stats['价格指数'] = (holiday_stats['平均价格'] / base_price).round(3)
        
        # 添加策略解读
        def get_strategy_interpretation(index_value):
            if index_value >= 1.2:
                return "🔥核心收割期"
            elif 0.95 <= index_value < 1.2:
                return "⚔️流量争夺期"
            else:
                return "📦市场激活期"
        
        holiday_stats['策略解读'] = holiday_stats['价格指数'].apply(get_strategy_interpretation)
        
        # 按价格指数排序
        holiday_stats = holiday_stats.sort_values('价格指数', ascending=False)
        
        print_to_md("| 节假日 | 平均价格 | 价格指数 | 促销次数 | 销售天数 | 策略解读 |")
        print_to_md("|:---|---:|---:|---:|---:|:---|")
        
        for holiday, row in holiday_stats.iterrows():
            print_to_md(f"| {holiday} | {row['平均价格']:.1f}元 | **{row['价格指数']:.3f}** | {row['促销次数']:.0f}次 | {row['总销售天数']:.0f}天 | {row['策略解读']} |")
        
        print_to_md()
    
    # 综合分析和策略建议
    print_to_md("### 节假日价格策略综合分析")
    print_to_md()
    
    # 统计各类节假日的价格指数分布
    all_holiday_data = []
    for ticket in core_tickets:
        base_ticket_data = promotions[promotions['促销票种'].str.contains(ticket, na=False)]
        if len(base_ticket_data) == 0:
            continue
            
        base_price = np.average(base_ticket_data['促销价格'], weights=base_ticket_data['销售天数'])
        holiday_ticket_data = holiday_promos_df[holiday_promos_df['促销票种'].str.contains(ticket, na=False)]
        
        if len(holiday_ticket_data) == 0:
            continue
            
        # 按节假日分组计算加权平均价格
        holiday_weighted_prices = holiday_ticket_data.groupby('适用节假日').apply(
            lambda g: weighted_average(g, '促销价格', '销售天数'), include_groups=False
        )
        holiday_indices = (holiday_weighted_prices / base_price)
        
        for holiday, index_val in holiday_indices.items():
            all_holiday_data.append({
                '票种': ticket,
                '节假日': holiday,
                '价格指数': index_val
            })
    
    if all_holiday_data:
        holiday_analysis_df = pd.DataFrame(all_holiday_data)
        
        # 按节假日统计平均价格指数
        holiday_summary = holiday_analysis_df.groupby('节假日')['价格指数'].agg(['mean', 'count']).round(3)
        holiday_summary.columns = ['平均价格指数', '票种数量']
        holiday_summary = holiday_summary.sort_values('平均价格指数', ascending=False)
        
        print_to_md("#### 各节假日价格指数排行")
        print_to_md("| 节假日 | 平均价格指数 | 涉及票种数 | 战略定位 | 我们的应对策略 |")
        print_to_md("|:---|---:|---:|:---|:---|")
        
        for holiday, row in holiday_summary.iterrows():
            avg_index = row['平均价格指数']
            ticket_count = row['票种数量']
            
            if avg_index >= 1.2:
                strategic_position = "**核心收割期**"
                our_strategy = "避免价格战，主打价值战和差异化，做好预售管理"
            elif 0.95 <= avg_index < 1.2:
                strategic_position = "流量争夺期"
                our_strategy = "正面竞争最佳时机，推出优势价格和捆绑产品"
            else:
                strategic_position = "市场激活期"
                our_strategy = "高度警惕价格战信号，快速跟进防御性产品"
            
            print_to_md(f"| {holiday} | **{avg_index:.3f}** | {ticket_count:.0f}个 | {strategic_position} | {our_strategy} |")
        
        print_to_md()
        
        # 重点关注的高价值节假日
        high_value_holidays = holiday_summary[holiday_summary['平均价格指数'] >= 1.2]
        if not high_value_holidays.empty:
            print_to_md("#### 🎯 重点关注：方特的核心收割期")
            for holiday, row in high_value_holidays.iterrows():
                print_to_md(f"- **{holiday}** (指数: {row['平均价格指数']:.3f}) - 方特在此期间具有强定价权，我们需要通过产品差异化竞争")
            print_to_md()
    
    print_to_md("### 收益管理战略建议")
    print_to_md()
    print_to_md("#### 🔥 针对核心收割期（指数≥1.2）")
    print_to_md("- **策略原则**: 避免硬碰硬价格战，损害利润")
    print_to_md("- **差异化竞争**: 推出成都项目独有的节庆主题活动、限定演艺、动物互动升级套餐")
    print_to_md("- **预售管理**: 通过早鸟票锁定价格敏感客群，早鸟期后坚决执行假日价格")
    print_to_md("- **价值传播**: 让游客觉得'虽然贵一点，但更值'")
    print_to_md()
    
    print_to_md("#### ⚔️ 针对流量争夺期（指数0.95-1.2）")
    print_to_md("- **策略原则**: 正面竞争的最佳时机")
    print_to_md("- **价格优势**: 推出比竞品略有优势的价格（如同价位多送餐饮券）")
    print_to_md("- **渠道激进**: 加大与OTA合作，推出渠道专享引流产品")
    print_to_md("- **抢夺摇摆客群**: 重点争夺价格敏感的计划型客群")
    print_to_md()
    
    print_to_md("#### 📦 针对市场激活期（指数<0.95）") 
    print_to_md("- **策略原则**: 高度警惕，可能是价格战信号")
    print_to_md("- **快速响应**: 推出防御性产品，防止客流被单方面虹吸")
    print_to_md("- **目的分析**: 判断是针对特定人群（大学生）还是特定时段（周中），选择性跟进")
    print_to_md("- **避免全面战争**: 防止陷入恶性价格竞争")
    print_to_md()
    
# 调用节假日价格指数分析
analyze_holiday_price_index()

# ==============================================================================
# 新增模块：年度策略演变分析 (YoY)
# ==============================================================================
def analyze_yoy_strategy_evolution(df):
    """
    对比分析同一节假日或主题活动在不同年份的定价策略变化。
    """
    print_to_md("## 年度策略演变分析 (YoY)")
    print_to_md("> **收益管理洞察**: 通过纵向对比，我们可以判断竞品的市场信心和策略方向。价格逐年上涨，代表其品牌溢价能力和市场需求旺盛；反之则可能面临竞争压力或策略调整。")
    print_to_md()

    # --- 1. 分析节假日的年度演变 ---
    print_to_md("### 核心节假日策略演变 (YoY)")
  
    # 确保 '适用节假日' 列存在且已被处理
    if '适用节假日' not in df.columns:
        print_to_md("- *无法进行节假日YoY分析，缺少'适用节假日'列。请确保此函数在节假日分析后运行。*")
        return

    holiday_promos = df.dropna(subset=['适用节假日']).explode('适用节假日')
    if not holiday_promos.empty:
        # 提取节假日基础名和年份
        holiday_promos_copy = holiday_promos.copy()
        holiday_split = holiday_promos_copy['适用节假日'].str.split('-', n=1, expand=True)
        if holiday_split.shape[1] >= 2:
            holiday_promos_copy['节假日基础名'] = holiday_split[0]
            holiday_promos_copy['年份'] = holiday_split[1]
        else:
            # 如果分割失败，跳过这部分分析
            print_to_md("- *节假日数据格式异常，无法进行YoY分析。*")
            print_to_md()
            return
      
        # 找到出现超过1次的节假日
        recurring_holidays = holiday_promos_copy['节假日基础名'].value_counts()
        yoy_holidays = recurring_holidays[recurring_holidays > 1].index

        if len(yoy_holidays) > 0:
            for holiday in yoy_holidays:
                print_to_md(f"#### {holiday} YoY 对比")
                holiday_data = holiday_promos_copy[holiday_promos_copy['节假日基础名'] == holiday]
              
                # 按年份聚合，计算核心指标
                yoy_stats = holiday_data.groupby('年份').agg(
                    平均促销价=('促销价格', 'mean'),
                    平均折扣率=('折扣率', 'mean'),
                    促销次数=('促销票种', 'count')
                ).round(2)
              
                # 计算同比变化
                yoy_stats['价格同比变化'] = yoy_stats['平均促销价'].pct_change().mul(100).round(1).fillna(0)
              
                print_to_md("| 年份 | 平均价格 | 价格同比 | 平均折扣率 | 促销次数 |")
                print_to_md("|:---|---:|---:|---:|---:|")
                for year, row in yoy_stats.iterrows():
                    change_str = f"{row['价格同比变化']:+.1f}%" if row['价格同比变化'] != 0 else "---"
                    print_to_md(f"| {year} | {row['平均促销价']:.0f}元 | **{change_str}** | {row['平均折扣率']:.1%} | {row['促销次数']:.0f}次 |")
                print_to_md()
        else:
            print_to_md("- *未发现可供同比分析的核心节假日。*")
        print_to_md()

    # --- 2. 分析主题活动的年度演变 ---
    print_to_md("### 主题活动策略演变 (YoY)")
  
    # 使用主题活动数据
    theme_promos = df.dropna(subset=['主题活动']).copy()
    if not theme_promos.empty:
        theme_promos['年份'] = theme_promos['使用日期（起）'].dt.year
      
        # 找到出现超过1次的主题活动
        recurring_themes = theme_promos['主题活动'].value_counts()
        yoy_themes = recurring_themes[recurring_themes > 1].index

        if len(yoy_themes) > 0:
            for theme in yoy_themes:
                print_to_md(f"#### {theme} YoY 对比")
                theme_data = theme_promos[theme_promos['主题活动'] == theme]
              
                yoy_stats = theme_data.groupby('年份').agg(
                    平均促销价=('促销价格', 'mean'),
                    平均折扣率=('折扣率', 'mean'),
                    平均预售天数=('预售天数', 'mean'),
                    促销次数=('促销票种', 'count')
                ).round(2)

                yoy_stats['价格同比变化'] = yoy_stats['平均促销价'].pct_change().mul(100).round(1).fillna(0)

                print_to_md("| 年份 | 平均价格 | 价格同比 | 平均折扣率 | 平均预售 | 促销次数 |")
                print_to_md("|:---|---:|---:|---:|---:|---:|")
                for year, row in yoy_stats.iterrows():
                    change_str = f"{row['价格同比变化']:+.1f}%" if row['价格同比变化'] != 0 else "---"
                    print_to_md(f"| {year} | {row['平均促销价']:.0f}元 | **{change_str}** | {row['平均折扣率']:.1%} | {row['平均预售天数']:.1f}天 | {row['促销次数']:.0f}次 |")
                print_to_md()
        else:
            print_to_md("- *未发现可供同比分析的主题活动。*")
  
    print_to_md()

# 调用年度策略演变分析
analyze_yoy_strategy_evolution(promotions)

# ==============================================================================

def create_strategy_quadrant_chart(df, filename="自贡方特_策略四象限图.png"):
    """
    根据处理好的数据，生成并保存产品策略四象限图。
    """
    if df.empty:
        print("数据为空，无法生成四象限图。")
        return

    # 设置中文显示字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 'SimHei' 是黑体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 准备数据
    df_plot = df.copy()
    df_plot['气泡大小'] = df_plot['预估收益指数']
  
    # 动态计算象限分割线（使用中位数，更稳健）
    x_median = df_plot['促销次数'].median()
    y_median = df_plot['平均促销价'].median()

    # 创建图表
    plt.figure(figsize=(18, 12))
  
    # 绘制散点图（气泡图）
    ax = plt.gca()
    scatter = sns.scatterplot(
        data=df_plot,
        x='促销次数',
        y='平均促销价',
        hue='策略标签',
        size='气泡大小',
        sizes=(200, 4000),  # 控制气泡大小范围
        palette='viridis',
        alpha=0.7,
        legend='full'
    )

    # 移除气泡尺寸图例，只保留颜色图例
    handles, labels = ax.get_legend_handles_labels()
    # 找到颜色图例的结束位置（通常在策略标签之后）
    color_legend_end = len(df_plot['策略标签'].unique()) + 1  # +1 for title
    ax.legend(handles[:color_legend_end], labels[:color_legend_end], 
              title='策略标签', bbox_to_anchor=(1.05, 1), loc='upper left')

    # 添加象限分割线
    plt.axvline(x=x_median, color='grey', linestyle='--', linewidth=1.5)
    plt.axhline(y=y_median, color='grey', linestyle='--', linewidth=1.5)

    # 智能适应性标注逻辑
    label_threshold = 2000  # 收益指数阈值，区分大气泡和小气泡
    
    for i, row in df_plot.iterrows():
        if row['预估收益指数'] >= label_threshold:
            # 大气泡：内部双行标注
            dual_line_text = f"{row['票种']}\n{row['预估收益指数']:.0f}"
            ax.text(row['促销次数'], row['平均促销价'], dual_line_text, 
                    ha='center', va='center', fontsize=9, color='white', weight='bold',
                    linespacing=1.2)
        else:
            # 小气泡：内数值、外名称的分离标注
            # 标注收益指数（白色文字，在气泡中心）
            ax.text(row['促销次数'], row['平均促销价'], f"{row['预估收益指数']:.0f}", 
                    ha='center', va='center', fontsize=8, color='white', weight='bold')
            
            # 计算Y轴偏移量，确保票种名称不与气泡重叠
            y_offset = (plt.ylim()[1] - plt.ylim()[0]) * 0.03  # 使用3%的Y轴范围作为偏移
            
            # 标注票种名称（在气泡正下方）
            ax.text(row['促销次数'], row['平均促销价'] - y_offset, row['票种'], 
                    ha='center', va='top', fontsize=9, color='darkgray')

    # 添加象限名称
    plt.text(plt.xlim()[1], plt.ylim()[1], ' 战略/测试区 ', ha='right', va='top', fontsize=16, color='grey', style='italic')
    plt.text(plt.xlim()[0], plt.ylim()[1], ' 现金牛区 ', ha='left', va='top', fontsize=16, color='grey', style='italic')
    plt.text(plt.xlim()[0], plt.ylim()[0], ' 常规/机会区 ', ha='left', va='bottom', fontsize=16, color='grey', style='italic')
    plt.text(plt.xlim()[1], plt.ylim()[0], ' 引流先锋区 ', ha='right', va='bottom', fontsize=16, color='grey', style='italic')

    # 设置图表标题和标签
    plt.title('产品策略四象限图', fontsize=24, pad=20)
    plt.xlabel('促销频率 (次数)', fontsize=14)
    plt.ylabel('平均促销价格 (元)', fontsize=14)
  
    # 优化布局并保存
    plt.tight_layout(rect=[0, 0, 0.85, 1]) # 为图例留出空间
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
  
    print(f"四象限图已成功保存为: {filename}")
    print_to_md(f"![产品策略四象限图](./{filename})")

# 准备并导出四象限图数据
print_to_md("## 四象限图")
print_to_md("正在生成产品策略四象限图...")
print_to_md()

# 从票种评级结果中提取四象限图所需数据
quadrant_data = []
for _, row in ticket_df.iterrows():
    quadrant_data.append({
        '票种': row['票种'],
        '促销次数': row['促销次数'],
        '平均促销价': row['平均促销价'],
        '策略重要性等级': row['策略重要性等级'],
        '综合评分': row['综合评分'],
        '策略标签': row['策略标签'],
        '预估收益指数': row['预估收益指数']
    })

# 创建四象限图数据DataFrame
quadrant_df = pd.DataFrame(quadrant_data)

# 导出为CSV文件
csv_filename = 'fangte_strategy_quadrant_data.csv'
quadrant_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')

# 调用绘图函数
create_strategy_quadrant_chart(quadrant_df, '自贡方特_策略四象限图.png')

print_to_md(f"四象限图数据已导出至: {csv_filename}")
print_to_md("### 数据说明")
print_to_md("- X轴: 促销次数（反映策略使用频度）")
print_to_md("- Y轴: 平均促销价格（反映价格定位）")
print_to_md("- 气泡大小: 预估收益指数")
print_to_md("- 气泡颜色: 主要策略标签")
print_to_md()

print_to_md("### 四象限图战略解读")
print_to_md("| 象限 | 特征 (X轴, Y轴) | 战略角色 | 我们的应对策略 |")
print_to_md("|:---|:---|:---|:---|")
print_to_md("| **现金牛区** | **高频 & 高价** | 稳定的利润贡献者，是其常规运营的压舱石。 | 需重点研究其产品价值，通过产品升级或服务差异化进行竞争，而非直接价格战。 |")
print_to_md("| **引流先锋区** | **高频 & 低价** | 核心的流量入口，用于拉新、激活市场，培养用户习惯。 | 必须保持高度警惕，适时推出同类竞品或防御性价格，防止核心客群流失。 |")
print_to_md("| **战略/测试区** | **低频 & 高价** | 可能是高价值新产品、测试性捆绑包或特殊节假日限定产品。 | 密切关注其转化效果，一旦成功可能成为新的现金牛。我们可以借鉴其思路进行产品创新。 |")
print_to_md("| **常规/机会区**| **低频 & 低价** | 机会型或战术性产品，用于填补特定节点或渠道的空白。 | 无需过度反应，但可分析其背后的特定目的（如渠道测试、清库存等），为我方提供参考。 |")
print_to_md()

# ==============================================================================
# 新增：价格带与价格锚点分布分析
# ==============================================================================
def analyze_price_points(df, core_tickets, output_file):
    """
    价格带与价格锚点分布分析
    """
    print_to_md("## 价格带与价格锚点分布分析")
    print_to_md("> **收益管理视角**: 通过分析核心票种的价格分布，识别竞品的'价格锚点'和'次锚点'，为我们的定价策略提供参考。")
    print_to_md()
    
    for ticket in core_tickets:
        print_to_md(f"### {ticket}")
        
        # 筛选该票种数据
        ticket_data = df[df['促销票种'].str.contains(ticket, na=False)]
        
        if len(ticket_data) == 0:
            print_to_md(f"- 无{ticket}数据")
            print_to_md()
            continue
            
        # 价格点统计
        price_points = ticket_data['促销价格'].value_counts().sort_index()
        print_to_md(f"#### {ticket}价格点分布")
        print_to_md("| 价格点 | 出现次数 | 累计频率 | 策略解读 |")
        print_to_md("|:---:|:---:|:---:|:---|")
        
        total_count = len(ticket_data)
        cumulative_freq = 0
        
        for price, count in price_points.head(10).items():
            cumulative_freq += count
            freq_pct = cumulative_freq / total_count
            
            # 策略解读
            if count >= 3 and freq_pct <= 0.5:
                interpretation = "主锚点 - 核心价格定位"
            elif count >= 2:
                interpretation = "次锚点 - 辅助价格选择"
            else:
                interpretation = "测试价格 - 市场试探"
                
            print_to_md(f"| {price}元 | {count}次 | {freq_pct:.1%} | {interpretation} |")
        
        print_to_md()
        
        # 生成价格分布直方图
        plt.figure(figsize=(12, 6))
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        sns.histplot(data=ticket_data, x='促销价格', bins=15, kde=True, alpha=0.7)
        plt.title(f'{ticket}促销价格分布')
        plt.xlabel('价格 (元)')
        plt.ylabel('频次')
        plt.grid(alpha=0.3)
        
        filename = f'{ticket}_价格分布图.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print_to_md(f"![{ticket}价格分布图](./{filename})")
        print_to_md()

# ==============================================================================
# 新增：周中 vs. 周末价格策略差异分析
# ==============================================================================
def analyze_weekday_weekend_strategy(df, output_file):
    """
    周中 vs. 周末价格策略差异分析
    """
    print_to_md("## 周中 vs. 周末价格策略差异分析")
    print_to_md("> **收益管理视角**: 通过对比不同时段的价格策略，识别竞品如何利用价格杠杆调控供需平衡。")
    print_to_md()
    
    def analyze_day_type(row):
        """分析使用日期的时段类型"""
        start_date = row['使用日期（起）']
        end_date = row['使用日期（止）']
        
        # 生成日期范围
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        total_days = len(date_range)
        
        # 计算周末天数（周六=5, 周日=6）
        weekend_days = sum(1 for d in date_range if d.weekday() >= 5)
        weekday_days = total_days - weekend_days
        
        if weekend_days == 0:
            return '纯周中'
        elif weekday_days == 0:
            return '纯周末'
        else:
            return '跨周期'
    
    # 应用时段类型分析
    df_copy = df.copy()
    df_copy['使用时段类型'] = df_copy.apply(analyze_day_type, axis=1)
    
    # 整体统计
    day_type_stats = df_copy.groupby('使用时段类型').agg({
        '促销价格': ['mean', 'count'],
        '折扣率': 'mean',
        '销售天数': 'mean'
    }).round(2)
    
    # 展平多级索引
    day_type_stats.columns = ['平均价格', '促销次数', '平均折扣率', '平均销售天数']
    
    print_to_md("### 整体时段策略对比")
    print_to_md("| 时段类型 | 促销次数 | 平均价格 | 平均折扣率 | 平均销售天数 | 策略解读 |")
    print_to_md("|:---|:---:|:---:|:---:|:---:|:---|")
    
    for day_type, row in day_type_stats.iterrows():
        count = int(row['促销次数'])
        price = row['平均价格']
        discount = row['平均折扣率']
        days = row['平均销售天数']
        
        # 策略解读
        if day_type == '纯周末' and discount > day_type_stats.loc['纯周中', '平均折扣率']:
            interpretation = "周末溢价策略 - 利用需求高峰提升单价"
        elif day_type == '纯周中' and discount < day_type_stats.loc['纯周末', '平均折扣率']:
            interpretation = "周中激活策略 - 通过低价刺激平日消费"
        else:
            interpretation = "均衡策略 - 价格相对稳定"
            
        print_to_md(f"| {day_type} | {count}次 | {price:.0f}元 | {discount:.1%} | {days:.1f}天 | {interpretation} |")
    
    print_to_md()
    
    # 核心票种的时段策略分析
    print_to_md("### 核心票种时段策略细分")
    
    for ticket in core_tickets:
        ticket_data = df_copy[df_copy['促销票种'].str.contains(ticket, na=False)]
        
        if len(ticket_data) == 0:
            continue
            
        ticket_day_stats = ticket_data.groupby('使用时段类型').agg({
            '促销价格': 'mean',
            '折扣率': 'mean'
        }).round(2)
        
        if len(ticket_day_stats) > 1:
            print_to_md(f"#### {ticket}")
            print_to_md("| 时段类型 | 平均价格 | 平均折扣率 | 价差 | 折扣差 |")
            print_to_md("|:---|:---:|:---:|:---:|:---:|")
            
            base_price = ticket_day_stats['促销价格'].mean()
            base_discount = ticket_day_stats['折扣率'].mean()
            
            for day_type, row in ticket_day_stats.iterrows():
                price = row['促销价格']
                discount = row['折扣率']
                price_diff = price - base_price
                discount_diff = discount - base_discount
                
                print_to_md(f"| {day_type} | {price:.0f}元 | {discount:.1%} | {price_diff:+.0f}元 | {discount_diff:+.1%} |")
            
            print_to_md()

# ==============================================================================
# 新增：预售期与折扣深度关系分析
# ==============================================================================
def analyze_lead_time_discount_relationship(df, output_file):
    """
    预售期与折扣深度关系分析
    """
    print_to_md("## 预售期与折扣深度关系分析")
    print_to_md("> **收益管理视角**: 探索'提前买是否更便宜'的规律，识别竞品的预售策略模型。")
    print_to_md()
    
    # 过滤有效数据（预售天数>=0且有折扣率数据）
    valid_data = df[(df['预售天数'] >= 0) & (df['折扣率'].notna())].copy()
    
    if len(valid_data) == 0:
        print_to_md("- 无有效的预售期与折扣数据")
        return
    
    # 计算相关系数
    correlation = valid_data['预售天数'].corr(valid_data['折扣率'])
    
    print_to_md("### 整体预售期与折扣关系")
    print_to_md(f"- **相关系数**: {correlation:.3f}")
    
    if correlation < -0.3:
        relationship_desc = "强负相关 - 预售期越长，折扣越大（价格越低）"
        strategy_insight = "典型早鸟策略，用价格换取需求确定性"
    elif correlation > 0.3:
        relationship_desc = "强正相关 - 预售期越长，价格越高"
        strategy_insight = "预售溢价策略，长预售期产品定位更高端"
    else:
        relationship_desc = "弱相关 - 预售期与价格无明显关系"
        strategy_insight = "预售期主要用于营销节奏控制，非价格工具"
    
    print_to_md(f"- **关系特征**: {relationship_desc}")
    print_to_md(f"- **策略洞察**: {strategy_insight}")
    print_to_md()
    
    # 按预售期区间分析
    def get_lead_time_category(days):
        if days == 0:
            return "即时购买"
        elif 1 <= days <= 7:
            return "短期预售(1-7天)"
        elif 8 <= days <= 30:
            return "中期预售(8-30天)"
        else:
            return "长期预售(>30天)"
    
    valid_data['预售期类别'] = valid_data['预售天数'].apply(get_lead_time_category)
    
    lead_time_stats = valid_data.groupby('预售期类别').agg({
        '折扣率': ['mean', 'count'],
        '促销价格': 'mean',
        '预售天数': 'mean'
    }).round(3)
    
    # 展平多级索引
    lead_time_stats.columns = ['平均折扣率', '样本数量', '平均价格', '平均预售天数']
    
    print_to_md("### 预售期区间策略分析")
    print_to_md("| 预售期类别 | 样本数 | 平均预售天数 | 平均折扣率 | 平均价格 | 策略特征 |")
    print_to_md("|:---|:---:|:---:|:---:|:---:|:---|")
    
    for category, row in lead_time_stats.iterrows():
        count = int(row['样本数量'])
        days = row['平均预售天数']
        discount = row['平均折扣率']
        price = row['平均价格']
        
        # 与即时购买对比
        if category != "即时购买" and "即时购买" in lead_time_stats.index:
            base_discount = lead_time_stats.loc['即时购买', '平均折扣率']
            if discount < base_discount:
                strategy_feature = "预售优惠策略"
            elif discount > base_discount:
                strategy_feature = "预售溢价策略"
            else:
                strategy_feature = "价格中性策略"
        else:
            strategy_feature = "基准参考"
        
        print_to_md(f"| {category} | {count} | {days:.1f}天 | {discount:.1%} | {price:.0f}元 | {strategy_feature} |")
    
    print_to_md()
    
    # 生成散点图
    plt.figure(figsize=(14, 8))
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 根据策略标签着色
    if '策略标签' in valid_data.columns:
        sns.scatterplot(data=valid_data, x='预售天数', y='折扣率', 
                       hue='策略标签', alpha=0.7, s=60)
    else:
        sns.scatterplot(data=valid_data, x='预售天数', y='折扣率', alpha=0.7, s=60)
    
    # 添加趋势线
    sns.regplot(data=valid_data, x='预售天数', y='折扣率', 
               scatter=False, color='red', line_kws={'linewidth': 2})
    
    plt.title('预售期与折扣深度关系图')
    plt.xlabel('预售天数 (天)')
    plt.ylabel('折扣率 (越低越优惠)')
    plt.gca().invert_yaxis()  # 反转Y轴，让优惠力度大的在上方
    plt.grid(alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    filename = '预售期与折扣关系图.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    
    print_to_md(f"![预售期与折扣关系图](./{filename})")
    print_to_md()

# ==============================================================================
# 调用新增的分析函数
# ==============================================================================

# 价格带与价格锚点分布分析
analyze_price_points(promotions, core_tickets, output_file)

# 周中 vs. 周末价格策略差异分析
analyze_weekday_weekend_strategy(promotions, output_file)

# 预售期与折扣深度关系分析
analyze_lead_time_discount_relationship(promotions, output_file)

# 添加数据计算口径说明
print_to_md("## 数据计算口径说明")
print_to_md()
print_to_md("### 基础数据来源")
print_to_md("- **主要数据表**: promotions（促销活动明细表）")
print_to_md("- **辅助数据表**: monthly_discounts（月度折扣汇总表）、monthly_sales_days（月度销售天数汇总表）")
print_to_md("- **数据时间范围**: 基于数据库中实际存储的促销活动时间范围")
print_to_md()

print_to_md("### 核心指标计算口径")
print_to_md()
print_to_md("#### 1. 折扣率计算")
print_to_md("```")
print_to_md("折扣率 = 促销价格 ÷ 门市价")
print_to_md("说明：折扣率越低代表优惠力度越大")
print_to_md("```")
print_to_md()

print_to_md("#### 2. 销售天数计算")
print_to_md("```")
print_to_md("销售天数 = (销售日期（止） - 销售日期（起）) + 1")
print_to_md("说明：包含起始日期和结束日期，按自然天数计算")
print_to_md("```")
print_to_md()

print_to_md("#### 3. 预售天数计算")
print_to_md("```")
print_to_md("预售天数 = 使用日期（起） - 销售日期（起）")
print_to_md("说明：计算从开始销售到开始使用的间隔天数，反映预售期长短")
print_to_md("数据清洗：负数或空值处理为0天（即买即用）")
print_to_md("```")
print_to_md()

print_to_md("#### 4. 策略标签分类标准")
print_to_md("- **闪促型**: 1-3天（制造稀缺感，瞬间引爆流量）")
print_to_md("- **节点型**: 4-10天（覆盖完整节假日或黄金周）")
print_to_md("- **波段型**: 11-30天（配合完整主题活动营销周期）")
print_to_md("- **长线型**: >30天（预售周期长、决策周期也长的产品）")
print_to_md()

print_to_md("#### 5. 票种策略重要性评级计算")
print_to_md("**权重分配：**")
print_to_md("- 平均折扣力度：50%（体现其作为价格武器的威力）")
print_to_md("- 促销频率：30%（体现其市场应用的广度）")
print_to_md("- 价格敏感度：20%（体现其撬动极限需求的能力）")
print_to_md()
print_to_md("**具体计算公式：**")
print_to_md("```")
print_to_md("平均折扣力度分数 = (1 - 平均折扣率) × 100")
print_to_md("促销频率分数 = (票种促销次数 ÷ 最大促销次数) × 100")
print_to_md("价格敏感度分数 = (1 - 最低折扣率) × 100")
print_to_md("综合评分 = 平均折扣力度分数 × 0.50 + 促销频率分数 × 0.30 + 价格敏感度分数 × 0.20")
print_to_md("```")
print_to_md()

print_to_md("#### 6. 策略重要性等级划分")
print_to_md("- **A+**: 综合评分 ≥ 85分")
print_to_md("- **A**: 75分 ≤ 综合评分 < 85分")
print_to_md("- **B+**: 65分 ≤ 综合评分 < 75分")
print_to_md("- **B**: 55分 ≤ 综合评分 < 65分")
print_to_md("- **C+**: 45分 ≤ 综合评分 < 55分")
print_to_md("- **C**: 综合评分 < 45分")
print_to_md()

print_to_md("#### 7. 预估收益指数计算")
print_to_md("```")
print_to_md("预估收益指数 = 平均促销价 × 促销次数")
print_to_md("说明：用于估算票种的潜在收益贡献度，数值越大表示收益潜力越高")
print_to_md("```")
print_to_md()

print_to_md("#### 8. 新增分析模块口径")
print_to_md("- **价格锚点分析**: 基于出现频次和累计频率识别主锚点和次锚点")
print_to_md("- **时段类型识别**: 根据使用日期范围中的周末天数比例分类")
print_to_md("- **预售期关系**: 使用皮尔逊相关系数分析预售天数与折扣率的线性关系")
print_to_md()

print_to_md("### 数据处理说明")
print_to_md()
print_to_md("#### 1. 票种匹配方式")
print_to_md("- 使用模糊匹配（str.contains）确保数据完整性")
print_to_md("- 核心票种包括：成人票、亲子票、双人票、家庭2大1小票、单人夜场票、儿童票")
print_to_md()

print_to_md("#### 2. 统计汇总方式")
print_to_md("- **众数计算**：价格锚点和策略标签采用众数，如无众数则使用中位数或混合型")
print_to_md("- **平均值计算**：所有均值类指标采用算术平均数")
print_to_md("- **时间分析**：基于pandas日期处理，确保时间计算准确性")
print_to_md()

print_to_md("#### 3. 四象限图说明")
print_to_md("- **X轴（促销频率）**：票种的促销次数")
print_to_md("- **Y轴（价格定位）**：票种的平均促销价格")
print_to_md("- **气泡大小**：预估收益指数")
print_to_md("- **气泡颜色**：主要策略标签")
print_to_md("- **象限分割线**：使用中位数作为分割点，增强统计稳健性")
print_to_md()

print_to_md("### 数据质量控制")
print_to_md("- 月度统计中标注样本状态（充足样本：≥3次，样本过小：<3次）")
print_to_md("- 缺失值处理：使用pandas的na=False参数处理字符串匹配中的空值")
print_to_md("- 异常值处理：通过分位数和统计分布识别数据合理性")
print_to_md()

print_to_md("---")
print_to_md(f"**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print_to_md("**数据分析工具**: Python pandas + sqlite3")
print_to_md("**制表人**: 海合安收益管理部")

# ==============================================================================

# 关闭数据库连接
conn.close()

# 关闭输出文件
output_file.close()

print("\n=== 分析完成 ===")
print("MD 报告已保存到: 自贡方特促销分析报告.md")
print("已添加详细的数据计算口径说明")
print("新增功能：")
print("1. 价格带与价格锚点分布分析")
print("2. 周中 vs. 周末价格策略差异分析") 
print("3. 预售期与折扣深度关系分析")