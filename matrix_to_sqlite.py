import pandas as pd
import sqlite3
import os

# ==============================================================================
# --- 配置区: 运行前请在这里修改 ---
# 1. 指定要处理的Excel文件名
excel_file = 'sales_days_matrix.xlsx'

# 2. 指定要在数据库中创建或更新的表名
table_name = 'monthly_sales_days'

# 3. 为数据值指定一个有意义的列名 (例如: 'discount_rate', 'sales_days')
value_column_name = 'sales_days'

# 4. 指定ID列的名称 (通常是票种)
id_column_name = 'ticket_type'
# ==============================================================================

# --- 数据库文件名 (通常保持不变) ---
db_file = 'Competitor_Zigong Fangte_Promotion Details.db'

# --- 核心逻辑 (V4 - 通用矩阵导入版) ---
def transform_and_load_generic_matrix(excel_path, db_path, table_name, id_col, val_col):
    """
    读取通用的Excel矩阵，转换为长表格式，并以智能追加的方式存入SQLite。
    """
    if not os.path.exists(excel_path):
        print(f"错误：找不到Excel文件 '{excel_path}'。")
        return

    try:
        print(f"--- 开始处理: {excel_path} ---")
        print(f"正在读取Excel文件...")
        df_wide = pd.read_excel(excel_path)
        
        print("正在将宽表矩阵转换为长表格式...")
        df_long = pd.melt(df_wide,
                          id_vars=[df_wide.columns[0]],
                          var_name='date_object',
                          value_name=val_col)
        
        df_long.dropna(subset=[val_col], inplace=True)
        # 仅保留数值大于0的行，因为销售天数为0没有记录意义
        df_long = df_long[pd.to_numeric(df_long[val_col], errors='coerce') > 0]

        if df_long.empty:
            print("文件中没有有效的（大于0的）数据可处理。")
            return

        print("正在进行数据清洗和格式化...")
        df_long['year'] = pd.to_datetime(df_long['date_object']).dt.year
        df_long['month'] = pd.to_datetime(df_long['date_object']).dt.month
        df_long[val_col] = pd.to_numeric(df_long[val_col])
        
        final_df = df_long[['year', 'month', df_wide.columns[0], val_col]]
        final_df.rename(columns={df_wide.columns[0]: id_col}, inplace=True)
        
        print("数据转换完成！准备写入数据库。")

        print(f"正在连接数据库: {db_path}...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        years_to_process = final_df['year'].unique()
        print(f"文件包含年份: {years_to_process}")

        print(f"准备向 '{table_name}' 表更新数据...")
        delete_query = f"DELETE FROM {table_name} WHERE year IN ({','.join(['?']*len(years_to_process))})"
        
        # 尝试执行删除，如果表不存在则跳过
        try:
            cursor.execute(delete_query, tuple(int(y) for y in years_to_process))
            print(f"已从表中删除 {cursor.rowcount} 条旧的年份数据，以备更新。")
        except sqlite3.OperationalError as e:
            if "no such table" in str(e):
                print(f"'{table_name}' 表不存在，将直接创建新表。")
            else:
                raise e # 抛出其他数据库错误

        print(f"正在将新数据追加到 '{table_name}' 表中...")
        final_df.to_sql(table_name, conn, if_exists='append', index=False)
        print("数据成功写入数据库！")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")

    finally:
        if 'conn' in locals() and conn:
            conn.commit()
            conn.close()
            print("数据库连接已关闭。")
            print(f"--- 处理完成 ---")

# --- 执行脚本 ---
if __name__ == "__main__":
    transform_and_load_generic_matrix(excel_file, db_file, table_name, id_column_name, value_column_name)
