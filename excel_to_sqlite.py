import pandas as pd
import sqlite3
import os

# --- 配置信息 ---
# 1. 指定你的Excel文件名
excel_file = 'promotions_history.xlsx'

# 2. 指定你想要生成的SQLite数据库文件名
db_file = 'Competitor_Zigong Fangte_Promotion Details.db'

# 3. 为数据库中的表指定一个名字
table_name = 'promotions'

# --- 核心逻辑 ---
def import_excel_to_sqlite(excel_path, db_path, table_name):
    """
    读取Excel文件并将其内容导入到SQLite数据库的指定表中。
    """
    # 检查Excel文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误：找不到Excel文件 '{excel_path}'。请确保文件名正确且文件与脚本在同一目录下。")
        return

    try:
        # 1. 使用pandas读取Excel文件
        # pandas会自动将Excel中的数据读取为一个名为DataFrame的数据结构
        print(f"正在读取Excel文件: {excel_path}...")
        df = pd.read_excel(excel_path)
        print("Excel文件读取成功！")

        # 显示数据列信息
        print(f"Excel文件包含以下列: {list(df.columns)}")
        print(f"原始数据行数: {len(df)}")
        
        # 数据清洗建议（可选，但推荐）
        # 你提供的原始数据中有一些完全重复的行，这里我们先去除它们
        df.drop_duplicates(inplace=True)
        print(f"数据去重后，剩余 {len(df)} 条记录。")
        
        # 处理日期列（如果存在使用日期列）
        date_columns = ['销售日期（起）', '销售日期（止）', '使用日期（起）', '使用日期（止）']
        for col in date_columns:
            if col in df.columns:
                # 确保日期格式正确，处理可能的格式问题
                try:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                    print(f"已处理日期列: {col}")
                except:
                    print(f"警告: 无法处理日期列 {col}，保持原格式")

        # 2. 连接到SQLite数据库
        # 如果db文件不存在，这行代码会自动创建它
        print(f"正在连接并创建数据库: {db_path}...")
        conn = sqlite3.connect(db_path)
        
        # 3. 将DataFrame数据写入SQL数据库
        # df.to_sql() 是一个非常强大的功能，可以自动完成建表和数据插入
        # if_exists='replace': 如果表已存在，则删除旧表，创建新表。这在重复运行时很方便。
        # index=False: 不将pandas的行索引写入数据库列
        print(f"正在将数据写入数据库的 '{table_name}' 表中...")
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        
        print("数据成功写入数据库！")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")

    finally:
        # 4. 关闭数据库连接
        if 'conn' in locals() and conn:
            conn.close()
            print("数据库连接已关闭。")

# --- 执行脚本 ---
if __name__ == "__main__":
    import_excel_to_sqlite(excel_file, db_file, table_name)

